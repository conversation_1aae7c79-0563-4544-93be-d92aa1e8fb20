import React from 'react';

// Simple Bar Chart Component (without external libraries)
const SimpleBarChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));

  return (
    <div className={`bg-white/90 backdrop-blur-sm rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 p-8 border border-warmGray-100/50 ${className}`}>
      <h3 className="text-xl font-bold text-warmGray-800 mb-8 tracking-tight">{title}</h3>
      <div className="space-y-6">
        {data.map((item, index) => (
          <div key={index} className="group">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-semibold text-warmGray-700">
                {item.label}
              </span>
              <span className="text-sm font-bold text-warmGray-800">
                {item.value}
              </span>
            </div>
            <div className="relative">
              <div className="w-full bg-warmGray-100 rounded-full h-3 overflow-hidden shadow-inner">
                <div
                  className="h-full bg-gradient-to-r from-[#E8C4A0] via-[#DDB892] to-[#D4A574] rounded-full transition-all duration-1000 ease-out shadow-sm group-hover:shadow-md animate-slideIn"
                  style={{
                    width: `${(item.value / maxValue) * 100}%`,
                    animationDelay: `${index * 0.1}s`
                  }}
                />
              </div>
              <div className="absolute top-0 left-0 w-full h-full flex items-center justify-end pr-2">
                <div className="text-xs font-medium text-warmGray-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {Math.round((item.value / maxValue) * 100)}%
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

    </div>
  );
};

// Simple Line Chart Component
const SimpleLineChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));
  const minValue = Math.min(...data.map(item => item.value));
  const range = maxValue - minValue || 1;

  return (
    <div className={`bg-white/90 backdrop-blur-sm rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 p-8 border border-warmGray-100/50 ${className}`}>
      <h3 className="text-xl font-bold text-warmGray-800 mb-8 tracking-tight">{title}</h3>
      <div className="relative h-56">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-warmGray-500 font-medium">
          <span className="bg-white/80 px-2 py-1 rounded">{maxValue}</span>
          <span className="bg-white/80 px-2 py-1 rounded">{Math.round((maxValue + minValue) / 2)}</span>
          <span className="bg-white/80 px-2 py-1 rounded">{minValue}</span>
        </div>

        {/* Chart area */}
        <div className="ml-12 h-full relative">
          <svg className="w-full h-full" viewBox="0 0 400 200">
            {/* Subtle grid lines */}
            {[0, 1, 2, 3, 4].map(i => (
              <line
                key={i}
                x1="0"
                y1={i * 40}
                x2="400"
                y2={i * 40}
                stroke="#f8fafc"
                strokeWidth="1"
                opacity="0.6"
              />
            ))}

            {/* Gradient definition */}
            <defs>
              <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#E8C4A0" />
                <stop offset="50%" stopColor="#DDB892" />
                <stop offset="100%" stopColor="#D4A574" />
              </linearGradient>
              <filter id="glow">
                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>

            {/* Area under the smooth curve */}
            <path
              d={(() => {
                if (data.length === 0) return '';

                const points = data.map((item, index) => ({
                  x: (index / (data.length - 1)) * 400,
                  y: 200 - ((item.value - minValue) / range) * 200
                }));

                if (points.length === 1) {
                  return `M ${points[0].x},${points[0].y} L ${points[0].x},200 L 0,200 Z`;
                }

                // Create smooth area fill matching the curve
                let path = `M ${points[0].x},${points[0].y}`;

                for (let i = 1; i < points.length; i++) {
                  const prev = points[i - 1];
                  const curr = points[i];
                  const next = points[i + 1];

                  const tension = 0.3;
                  const cp1x = prev.x + (curr.x - (points[i - 2] ? points[i - 2].x : prev.x)) * tension;
                  const cp1y = prev.y + (curr.y - (points[i - 2] ? points[i - 2].y : prev.y)) * tension;
                  const cp2x = curr.x - (next ? next.x - prev.x : curr.x - prev.x) * tension;
                  const cp2y = curr.y - (next ? next.y - prev.y : curr.y - prev.y) * tension;

                  path += ` C ${cp1x},${cp1y} ${cp2x},${cp2y} ${curr.x},${curr.y}`;
                }

                // Close the area to the bottom
                path += ` L ${points[points.length - 1].x},200 L ${points[0].x},200 Z`;

                return path;
              })()}
              fill="url(#lineGradient)"
              opacity="0.08"
            />

            {/* Smooth wave line using cubic bezier curves */}
            <path
              fill="none"
              stroke="url(#lineGradient)"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
              filter="url(#glow)"
              d={(() => {
                if (data.length === 0) return '';

                const points = data.map((item, index) => ({
                  x: (index / (data.length - 1)) * 400,
                  y: 200 - ((item.value - minValue) / range) * 200
                }));

                if (points.length === 1) {
                  return `M ${points[0].x},${points[0].y}`;
                }

                // Create smooth curve using cubic bezier
                let path = `M ${points[0].x},${points[0].y}`;

                for (let i = 1; i < points.length; i++) {
                  const prev = points[i - 1];
                  const curr = points[i];
                  const next = points[i + 1];

                  // Calculate control points for smooth curves
                  const tension = 0.3; // Adjust this for more/less curve
                  const cp1x = prev.x + (curr.x - (points[i - 2] ? points[i - 2].x : prev.x)) * tension;
                  const cp1y = prev.y + (curr.y - (points[i - 2] ? points[i - 2].y : prev.y)) * tension;
                  const cp2x = curr.x - (next ? next.x - prev.x : curr.x - prev.x) * tension;
                  const cp2y = curr.y - (next ? next.y - prev.y : curr.y - prev.y) * tension;

                  path += ` C ${cp1x},${cp1y} ${cp2x},${cp2y} ${curr.x},${curr.y}`;
                }

                return path;
              })()}
              className="animate-drawLine"
            />
          </svg>
        </div>

        {/* X-axis labels */}
        <div className="ml-12 mt-4 flex justify-between text-xs text-warmGray-600 font-medium">
          {data.map((item, index) => (
            <span key={index} className="bg-white/60 px-2 py-1 rounded">{item.label}</span>
          ))}
        </div>
      </div>

    </div>
  );
};

// Rating Distribution Chart
const RatingDistributionChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No ratings data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.count));
  const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#16a34a']; // Red to Green

  return (
    <div className={`bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-warmGray-800 mb-6">{title}</h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-3">
            <div className="flex items-center space-x-1 w-16">
              <span className="text-sm font-medium text-warmGray-700">{item.rating}</span>
              <span className="text-yellow-400">★</span>
            </div>
            <div className="flex-1 bg-warmGray-200 rounded-full h-6 relative overflow-hidden">
              <div
                className="h-full rounded-full transition-all duration-700 ease-out"
                style={{ 
                  width: `${(item.count / maxValue) * 100}%`,
                  backgroundColor: colors[index] || '#E8C4A0'
                }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xs font-medium text-white">
                  {item.count}
                </span>
              </div>
            </div>
            <div className="w-12 text-xs text-warmGray-500">
              {maxValue > 0 ? Math.round((item.count / maxValue) * 100) : 0}%
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Full Card Size Chart - Fills entire card dimensions
const EvaluationTrendsChart = ({ data, title, className = '' }) => {
  // Handle empty or invalid data
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl shadow-sm border border-gray-200 h-full w-full flex flex-col ${className}`} style={{ padding: '10px' }}>
        <h3 className="text-lg font-semibold text-gray-800 mb-2 flex-shrink-0">{title}</h3>
        <div className="flex-1 flex items-center justify-center" style={{ padding: '5px' }}>
          <p className="text-gray-500 text-sm">No evaluation data available</p>
        </div>
      </div>
    );
  }

  // Process data for chart
  const values = data.map(item => item.value || 0);
  const labels = data.map(item => item.label || '');
  const maxValue = Math.max(...values, 1);
  const minValue = Math.min(...values, 0);

  // Dynamic chart dimensions - use full card space
  const titleHeight = 30; // Space for title
  const yAxisWidth = 20; // Space for Y-axis labels
  const xAxisHeight = 20; // Space for X-axis labels

  // Calculate available space (will be set via CSS to fill card)
  const availableWidth = '100%';
  const availableHeight = `calc(100% - ${titleHeight}px)`;

  // For SVG calculations, we'll use a base size that scales
  const baseWidth = 300;
  const baseHeight = 120;
  const chartAreaWidth = baseWidth - yAxisWidth;
  const chartAreaHeight = baseHeight - xAxisHeight;

  // Calculate data points within the chart area
  const dataPoints = values.map((value, index) => {
    const x = yAxisWidth + (index / Math.max(values.length - 1, 1)) * chartAreaWidth;
    const y = ((value - minValue) / Math.max(maxValue - minValue, 1)) * chartAreaHeight;
    return { x, y: chartAreaHeight - y, value, label: labels[index] };
  });

  // Create smooth curve path
  const createSmoothPath = (points) => {
    if (points.length < 2) return '';

    let path = `M ${points[0].x} ${points[0].y}`;

    for (let i = 1; i < points.length; i++) {
      const current = points[i];
      const previous = points[i - 1];

      // Smooth curve using quadratic bezier
      const cpX = (previous.x + current.x) / 2;
      const cpY = (previous.y + current.y) / 2;

      if (i === 1) {
        path += ` Q ${cpX} ${previous.y} ${current.x} ${current.y}`;
      } else {
        path += ` T ${current.x} ${current.y}`;
      }
    }

    return path;
  };

  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-200 h-full w-full flex flex-col ${className}`} style={{ padding: '10px' }}>
      <h3 className="text-lg font-semibold text-gray-800 mb-2 flex-shrink-0">{title}</h3>

      {/* Chart container fills remaining space with internal padding */}
      <div className="flex-1 w-full" style={{ padding: '5px' }}>
        <svg
          width="100%"
          height="100%"
          viewBox={`0 0 ${baseWidth} ${baseHeight}`}
          preserveAspectRatio="none"
          className="w-full h-full"
        >
          <defs>
            <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#3b82f6" />
              <stop offset="100%" stopColor="#1d4ed8" />
            </linearGradient>
            <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.1" />
              <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.02" />
            </linearGradient>
          </defs>

          {/* Y-axis labels */}
          <text x="5" y="15" fontSize="10" fill="#6b7280" textAnchor="start">{maxValue}</text>
          <text x="5" y={chartAreaHeight / 2 + 5} fontSize="10" fill="#6b7280" textAnchor="start">
            {Math.round((maxValue + minValue) / 2)}
          </text>
          <text x="5" y={chartAreaHeight + 5} fontSize="10" fill="#6b7280" textAnchor="start">{minValue}</text>

          {/* Horizontal grid lines */}
          {[0.25, 0.5, 0.75].map((ratio, index) => (
            <line
              key={index}
              x1={yAxisWidth}
              y1={chartAreaHeight * ratio}
              x2={yAxisWidth + chartAreaWidth}
              y2={chartAreaHeight * ratio}
              stroke="#f1f5f9"
              strokeWidth="1"
            />
          ))}

          {/* Area under curve */}
          {dataPoints.length > 1 && (
            <path
              d={`${createSmoothPath(dataPoints)} L ${dataPoints[dataPoints.length - 1].x} ${chartAreaHeight} L ${dataPoints[0].x} ${chartAreaHeight} Z`}
              fill="url(#areaGradient)"
            />
          )}

          {/* Main trend line */}
          <path
            d={createSmoothPath(dataPoints)}
            fill="none"
            stroke="url(#chartGradient)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />

          {/* X-axis labels */}
          {labels.map((label, index) => {
            const x = yAxisWidth + (index / Math.max(labels.length - 1, 1)) * chartAreaWidth;
            return (
              <text
                key={index}
                x={x}
                y={chartAreaHeight + 15}
                fontSize="10"
                fill="#6b7280"
                textAnchor="middle"
              >
                {label}
              </text>
            );
          })}
        </svg>
      </div>
    </div>
  );
};

export { SimpleBarChart, SimpleLineChart, RatingDistributionChart, EvaluationTrendsChart };
