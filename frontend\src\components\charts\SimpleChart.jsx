import React from 'react';

// Simple Bar Chart Component (without external libraries)
const SimpleBarChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));

  return (
    <div className={`bg-white/90 backdrop-blur-sm rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 p-8 border border-warmGray-100/50 ${className}`}>
      <h3 className="text-xl font-bold text-warmGray-800 mb-8 tracking-tight">{title}</h3>
      <div className="space-y-6">
        {data.map((item, index) => (
          <div key={index} className="group">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-semibold text-warmGray-700">
                {item.label}
              </span>
              <span className="text-sm font-bold text-warmGray-800">
                {item.value}
              </span>
            </div>
            <div className="relative">
              <div className="w-full bg-warmGray-100 rounded-full h-3 overflow-hidden shadow-inner">
                <div
                  className="h-full bg-gradient-to-r from-[#E8C4A0] via-[#DDB892] to-[#D4A574] rounded-full transition-all duration-1000 ease-out shadow-sm group-hover:shadow-md animate-slideIn"
                  style={{
                    width: `${(item.value / maxValue) * 100}%`,
                    animationDelay: `${index * 0.1}s`
                  }}
                />
              </div>
              <div className="absolute top-0 left-0 w-full h-full flex items-center justify-end pr-2">
                <div className="text-xs font-medium text-warmGray-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {Math.round((item.value / maxValue) * 100)}%
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

    </div>
  );
};

// Simple Line Chart Component
const SimpleLineChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));
  const minValue = Math.min(...data.map(item => item.value));
  const range = maxValue - minValue || 1;

  return (
    <div className={`bg-white/90 backdrop-blur-sm rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 p-8 border border-warmGray-100/50 ${className}`}>
      <h3 className="text-xl font-bold text-warmGray-800 mb-8 tracking-tight">{title}</h3>
      <div className="relative h-56">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-warmGray-500 font-medium">
          <span className="bg-white/80 px-2 py-1 rounded">{maxValue}</span>
          <span className="bg-white/80 px-2 py-1 rounded">{Math.round((maxValue + minValue) / 2)}</span>
          <span className="bg-white/80 px-2 py-1 rounded">{minValue}</span>
        </div>

        {/* Chart area */}
        <div className="ml-12 h-full relative">
          <svg className="w-full h-full" viewBox="0 0 400 200">
            {/* Subtle grid lines */}
            {[0, 1, 2, 3, 4].map(i => (
              <line
                key={i}
                x1="0"
                y1={i * 40}
                x2="400"
                y2={i * 40}
                stroke="#f8fafc"
                strokeWidth="1"
                opacity="0.6"
              />
            ))}

            {/* Gradient definition */}
            <defs>
              <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#E8C4A0" />
                <stop offset="50%" stopColor="#DDB892" />
                <stop offset="100%" stopColor="#D4A574" />
              </linearGradient>
              <filter id="glow">
                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>

            {/* Area under the smooth curve */}
            <path
              d={(() => {
                if (data.length === 0) return '';

                const points = data.map((item, index) => ({
                  x: (index / (data.length - 1)) * 400,
                  y: 200 - ((item.value - minValue) / range) * 200
                }));

                if (points.length === 1) {
                  return `M ${points[0].x},${points[0].y} L ${points[0].x},200 L 0,200 Z`;
                }

                // Create smooth area fill matching the curve
                let path = `M ${points[0].x},${points[0].y}`;

                for (let i = 1; i < points.length; i++) {
                  const prev = points[i - 1];
                  const curr = points[i];
                  const next = points[i + 1];

                  const tension = 0.3;
                  const cp1x = prev.x + (curr.x - (points[i - 2] ? points[i - 2].x : prev.x)) * tension;
                  const cp1y = prev.y + (curr.y - (points[i - 2] ? points[i - 2].y : prev.y)) * tension;
                  const cp2x = curr.x - (next ? next.x - prev.x : curr.x - prev.x) * tension;
                  const cp2y = curr.y - (next ? next.y - prev.y : curr.y - prev.y) * tension;

                  path += ` C ${cp1x},${cp1y} ${cp2x},${cp2y} ${curr.x},${curr.y}`;
                }

                // Close the area to the bottom
                path += ` L ${points[points.length - 1].x},200 L ${points[0].x},200 Z`;

                return path;
              })()}
              fill="url(#lineGradient)"
              opacity="0.08"
            />

            {/* Smooth wave line using cubic bezier curves */}
            <path
              fill="none"
              stroke="url(#lineGradient)"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
              filter="url(#glow)"
              d={(() => {
                if (data.length === 0) return '';

                const points = data.map((item, index) => ({
                  x: (index / (data.length - 1)) * 400,
                  y: 200 - ((item.value - minValue) / range) * 200
                }));

                if (points.length === 1) {
                  return `M ${points[0].x},${points[0].y}`;
                }

                // Create smooth curve using cubic bezier
                let path = `M ${points[0].x},${points[0].y}`;

                for (let i = 1; i < points.length; i++) {
                  const prev = points[i - 1];
                  const curr = points[i];
                  const next = points[i + 1];

                  // Calculate control points for smooth curves
                  const tension = 0.3; // Adjust this for more/less curve
                  const cp1x = prev.x + (curr.x - (points[i - 2] ? points[i - 2].x : prev.x)) * tension;
                  const cp1y = prev.y + (curr.y - (points[i - 2] ? points[i - 2].y : prev.y)) * tension;
                  const cp2x = curr.x - (next ? next.x - prev.x : curr.x - prev.x) * tension;
                  const cp2y = curr.y - (next ? next.y - prev.y : curr.y - prev.y) * tension;

                  path += ` C ${cp1x},${cp1y} ${cp2x},${cp2y} ${curr.x},${curr.y}`;
                }

                return path;
              })()}
              className="animate-drawLine"
            />
          </svg>
        </div>

        {/* X-axis labels */}
        <div className="ml-12 mt-4 flex justify-between text-xs text-warmGray-600 font-medium">
          {data.map((item, index) => (
            <span key={index} className="bg-white/60 px-2 py-1 rounded">{item.label}</span>
          ))}
        </div>
      </div>

    </div>
  );
};

// Rating Distribution Chart
const RatingDistributionChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-xl p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-warmGray-800 mb-4">{title}</h3>
        <div className="text-center py-8">
          <p className="text-warmGray-500">No ratings data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.count));
  const colors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#16a34a']; // Red to Green

  return (
    <div className={`bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-warmGray-800 mb-6">{title}</h3>
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center space-x-3">
            <div className="flex items-center space-x-1 w-16">
              <span className="text-sm font-medium text-warmGray-700">{item.rating}</span>
              <span className="text-yellow-400">★</span>
            </div>
            <div className="flex-1 bg-warmGray-200 rounded-full h-6 relative overflow-hidden">
              <div
                className="h-full rounded-full transition-all duration-700 ease-out"
                style={{ 
                  width: `${(item.count / maxValue) * 100}%`,
                  backgroundColor: colors[index] || '#E8C4A0'
                }}
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-xs font-medium text-white">
                  {item.count}
                </span>
              </div>
            </div>
            <div className="w-12 text-xs text-warmGray-500">
              {maxValue > 0 ? Math.round((item.count / maxValue) * 100) : 0}%
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Wave Chart Component with Sinusoidal Curves - Compact Design
const WaveChart = ({ data, title, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-2xl p-3 border border-gray-100 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        <div className="text-center py-4">
          <p className="text-gray-500">No data available</p>
        </div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(item => item.value));
  const minValue = Math.min(...data.map(item => item.value));
  const range = maxValue - minValue || 1;

  // Create smooth wave path using spline interpolation
  const createWavePath = (points, tension = 0.3) => {
    if (points.length < 2) return '';
    if (points.length === 2) {
      return `M ${points[0].x},${points[0].y} L ${points[1].x},${points[1].y}`;
    }

    let path = `M ${points[0].x},${points[0].y}`;

    for (let i = 1; i < points.length; i++) {
      const prev = points[i - 1];
      const curr = points[i];
      const next = points[i + 1];
      const prevPrev = points[i - 2];

      // Calculate control points for smooth spline
      let cp1x, cp1y, cp2x, cp2y;

      if (i === 1) {
        cp1x = prev.x + (curr.x - prev.x) * tension;
        cp1y = prev.y + (curr.y - prev.y) * tension;
      } else {
        cp1x = prev.x + (curr.x - prevPrev.x) * tension * 0.5;
        cp1y = prev.y + (curr.y - prevPrev.y) * tension * 0.5;
      }

      if (i === points.length - 1) {
        cp2x = curr.x - (curr.x - prev.x) * tension;
        cp2y = curr.y - (curr.y - prev.y) * tension;
      } else {
        cp2x = curr.x - (next.x - prev.x) * tension * 0.5;
        cp2y = curr.y - (next.y - prev.y) * tension * 0.5;
      }

      path += ` C ${cp1x},${cp1y} ${cp2x},${cp2y} ${curr.x},${curr.y}`;
    }

    return path;
  };

  // Calculate points with minimal padding
  const chartWidth = 320;
  const chartHeight = 120;
  const points = data.map((item, index) => ({
    x: (index / (data.length - 1)) * chartWidth + 10,
    y: chartHeight - ((item.value - minValue) / range) * (chartHeight - 20) + 10
  }));

  return (
    <div className={`bg-white rounded-2xl p-3 border border-gray-100 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <div className="relative">
        {/* Y-axis labels */}
        <div className="absolute left-0 top-0 h-32 flex flex-col justify-between text-xs text-gray-400 w-5">
          <span>{maxValue}</span>
          <span>{Math.round((maxValue + minValue) / 2)}</span>
          <span>{minValue}</span>
        </div>

        {/* Chart area */}
        <div className="ml-5 relative">
          <svg className="w-full h-32" viewBox="0 0 340 140" preserveAspectRatio="xMidYMid meet">
            {/* Gradient definitions */}
            <defs>
              <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#3b82f6" />
                <stop offset="100%" stopColor="#1d4ed8" />
              </linearGradient>
              <linearGradient id="waveAreaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.1" />
                <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.02" />
              </linearGradient>
            </defs>

            {/* Minimal grid lines */}
            {[1, 2].map(i => (
              <line
                key={i}
                x1="10"
                y1={i * 40 + 10}
                x2="330"
                y2={i * 40 + 10}
                stroke="#f1f5f9"
                strokeWidth="1"
              />
            ))}

            {/* Area under the wave */}
            <path
              d={createWavePath(points) + ` L ${points[points.length - 1].x},130 L ${points[0].x},130 Z`}
              fill="url(#waveAreaGradient)"
            />

            {/* Main wave line */}
            <path
              fill="none"
              stroke="url(#waveGradient)"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              d={createWavePath(points)}
            />
          </svg>
        </div>

        {/* X-axis labels */}
        <div className="ml-5 mt-1 flex justify-between text-xs text-gray-500">
          {data.map((item, index) => (
            <span key={index}>
              {item.label}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

export { SimpleBarChart, SimpleLineChart, RatingDistributionChart, WaveChart };
